# 加班审批表生成器使用说明

## 功能概述

这是一个基于Web的加班审批表生成工具，可以帮助用户快速创建标准格式的加班审批Word文档。

## 主要功能

### 1. 基础信息输入
- **工作部门**：输入申请人所在部门
- **加班人**：输入申请加班的员工姓名
- **加班事由**：详细描述加班的原因和必要性
- **加班地点**：指定加班的具体地点

### 2. 智能日历选择
- **月份选择器**：选择需要申请加班的年月
- **双日历显示**：
  - 左侧显示工作日日历（周一至周五）
  - 右侧显示休息日/节假日日历（周六、周日）
- **多日期选择**：可以在任一日历中选择多个日期
- **实时预览**：已选择的日期会实时显示在左侧面板

### 3. Word文档生成
- **一键导出**：填写完信息并选择日期后，点击按钮即可生成Word文档
- **标准格式**：生成的文档符合企业加班审批表的标准格式
- **自动命名**：文件名格式为"部门-姓名-加班审批表-年月.docx"

## 使用步骤

1. **打开应用**：在浏览器中打开 `overtime-approval.html` 文件

2. **填写基础信息**：
   - 在左侧表单中依次填写工作部门、加班人、加班事由、加班地点

3. **选择加班时间**：
   - 使用月份选择器选择需要申请的年月
   - 在右侧日历中点击需要加班的日期（可多选）
   - 选中的日期会在左侧显示，并标注星期几

4. **生成文档**：
   - 确认所有信息填写完整
   - 点击"生成并下载Word文档"按钮
   - 等待文档生成并自动下载

## 界面说明

### 左侧面板（表单区域）
- 基础信息输入表单
- 已选择日期的实时显示
- 清空选择按钮
- 导出按钮

### 右侧面板（日历区域）
- 月份选择器
- 工作日日历（蓝色主题）
- 休息日日历（红色主题）

## 注意事项

1. **必填信息**：所有基础信息字段都必须填写，且至少选择一个加班日期，导出按钮才会激活

2. **浏览器兼容性**：建议使用现代浏览器（Chrome、Firefox、Edge等）

3. **网络连接**：首次使用需要网络连接以加载必要的JavaScript库

4. **文件下载**：生成的Word文档会自动下载到浏览器的默认下载文件夹

## 技术特性

- **响应式设计**：支持不同屏幕尺寸的设备
- **实时验证**：表单填写状态实时验证
- **加载状态**：文档生成过程中显示加载动画
- **错误处理**：完善的错误提示和处理机制

## 故障排除

### 如果导出按钮无法点击：
- 检查是否填写了所有必填信息
- 确认是否选择了至少一个加班日期

### 如果文档生成失败：
- 检查网络连接是否正常
- 刷新页面重新尝试
- 确认浏览器支持文件下载功能

### 如果日历显示异常：
- 确认已选择了有效的年月
- 刷新页面重新加载

## 更新日志

- v1.0：基础功能实现，支持双日历选择和Word文档生成
- 界面优化和用户体验改进
- 添加加载状态和错误处理
- 支持日期清空和重新选择功能
