<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加班审批表模板示例</title>
    <style>
        body {
            font-family: '宋体', SimSun, serif;
            font-size: 14px;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        .template-container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .title {
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 30px;
            color: #2c3e50;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        .info-table td {
            border: 1px solid #333;
            padding: 12px;
            vertical-align: middle;
        }
        .label {
            background-color: #f8f9fa;
            width: 20%;
            font-weight: bold;
            text-align: center;
        }
        .approval-section {
            margin-top: 40px;
        }
        .approval-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        .approval-table td {
            border: 1px solid #333;
            padding: 20px;
            vertical-align: top;
            height: 120px;
        }
        .note {
            margin-top: 30px;
            font-size: 12px;
            line-height: 1.8;
        }
        .placeholder {
            background-color: #fff3cd;
            color: #856404;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
            font-weight: bold;
        }
        .instruction {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .download-section {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .download-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
        }
        .download-btn:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="template-container">
        <div class="instruction">
            <h3>📋 模板使用说明</h3>
            <p>这是一个加班审批表模板示例。黄色高亮的部分是占位符，在实际的Word模板中，请使用相同的占位符格式。</p>
            <p><strong>重要：</strong>在Word中创建模板时，请直接输入占位符文本（如 {{部门}}），不要添加任何特殊格式。</p>
        </div>

        <div class="title">加班审批表</div>
        
        <table class="info-table">
            <tr>
                <td class="label">工作部门</td>
                <td><span class="placeholder">{{部门}}</span></td>
                <td class="label">加班人</td>
                <td><span class="placeholder">{{姓名}}</span></td>
            </tr>
            <tr>
                <td class="label">加班事由</td>
                <td colspan="3"><span class="placeholder">{{事由}}</span></td>
            </tr>
            <tr>
                <td class="label">加班地点</td>
                <td colspan="3"><span class="placeholder">{{地点}}</span></td>
            </tr>
            <tr>
                <td class="label">加班时间</td>
                <td colspan="3"><span class="placeholder">{{日期}}</span></td>
            </tr>
            <tr>
                <td class="label">申请日期</td>
                <td colspan="3"><span class="placeholder">{{当前日期}}</span></td>
            </tr>
        </table>
        
        <div class="approval-section">
            <table class="approval-table">
                <tr>
                    <td style="width: 50%;">
                        <strong>部门负责人意见：</strong><br><br><br><br><br>
                        签字：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日期：
                    </td>
                    <td style="width: 50%;">
                        <strong>分管领导意见：</strong><br><br><br><br><br>
                        签字：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日期：
                    </td>
                </tr>
            </table>
        </div>
        
        <div class="note">
            <strong>备注：</strong><br>
            1. 加班需提前申请，经部门负责人或分管领导批准后方可执行；<br>
            2. 本表一式两份，一份报人事部门备案，一份部门留存；<br>
            3. 加班时间超过2小时的，需填写详细的工作内容说明。
        </div>
    </div>

    <div class="download-section">
        <h3>📥 如何使用这个模板</h3>
        <ol style="text-align: left; display: inline-block;">
            <li>复制上面的表格内容到Microsoft Word中</li>
            <li>调整格式、字体、表格样式等</li>
            <li>确保占位符格式正确（使用双花括号）</li>
            <li>保存为 .docx 格式</li>
            <li>在加班审批表生成器中上传使用</li>
        </ol>
        
        <p><strong>支持的占位符：</strong></p>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 15px 0;">
            <div style="background: #fff3cd; padding: 8px; border-radius: 4px; text-align: center;">
                <code>{{部门}}</code><br><small>工作部门</small>
            </div>
            <div style="background: #fff3cd; padding: 8px; border-radius: 4px; text-align: center;">
                <code>{{姓名}}</code><br><small>加班人姓名</small>
            </div>
            <div style="background: #fff3cd; padding: 8px; border-radius: 4px; text-align: center;">
                <code>{{事由}}</code><br><small>加班事由</small>
            </div>
            <div style="background: #fff3cd; padding: 8px; border-radius: 4px; text-align: center;">
                <code>{{地点}}</code><br><small>加班地点</small>
            </div>
            <div style="background: #fff3cd; padding: 8px; border-radius: 4px; text-align: center;">
                <code>{{日期}}</code><br><small>加班日期列表</small>
            </div>
            <div style="background: #fff3cd; padding: 8px; border-radius: 4px; text-align: center;">
                <code>{{当前日期}}</code><br><small>申请日期</small>
            </div>
        </div>
        
        <button class="download-btn" onclick="copyTemplate()">📋 复制模板内容</button>
    </div>

    <script>
        function copyTemplate() {
            // 创建模板文本
            const templateText = `加班审批表

工作部门：{{部门}}                    加班人：{{姓名}}
加班事由：{{事由}}
加班地点：{{地点}}
加班时间：{{日期}}
申请日期：{{当前日期}}

部门负责人意见：                     分管领导意见：




签字：                              签字：
日期：                              日期：

备注：
1. 加班需提前申请，经部门负责人或分管领导批准后方可执行；
2. 本表一式两份，一份报人事部门备案，一份部门留存；
3. 加班时间超过2小时的，需填写详细的工作内容说明。`;

            // 复制到剪贴板
            navigator.clipboard.writeText(templateText).then(() => {
                alert('模板内容已复制到剪贴板！\n\n请粘贴到Word中，调整格式后保存为.docx文件。');
            }).catch(() => {
                // 备用方法
                const textArea = document.createElement('textarea');
                textArea.value = templateText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('模板内容已复制到剪贴板！\n\n请粘贴到Word中，调整格式后保存为.docx文件。');
            });
        }
    </script>
</body>
</html>
