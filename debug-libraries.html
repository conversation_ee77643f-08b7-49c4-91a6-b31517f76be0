<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>库加载调试工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .test-item {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-item h3 {
            margin-top: 0;
            color: #34495e;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            font-weight: bold;
            display: inline-block;
            margin-left: 10px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .status.loading {
            background: #fff3cd;
            color: #856404;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>库加载调试工具</h1>
        
        <div class="test-item">
            <h3>1. 基础环境检测</h3>
            <p>检测浏览器基础功能支持情况</p>
            <button onclick="testBasicEnvironment()">开始检测</button>
            <span id="basic-status" class="status loading">等待检测</span>
            <div id="basic-log" class="log" style="display: none;"></div>
        </div>
        
        <div class="test-item">
            <h3>2. CDN连接测试</h3>
            <p>测试不同CDN的连接状况</p>
            <button onclick="testCDNConnections()">测试连接</button>
            <span id="cdn-status" class="status loading">等待测试</span>
            <div id="cdn-log" class="log" style="display: none;"></div>
        </div>
        
        <div class="test-item">
            <h3>3. docx库加载测试</h3>
            <p>测试docx库的加载和基本功能</p>
            <button onclick="testDocxLibrary()">测试docx库</button>
            <span id="docx-status" class="status loading">等待测试</span>
            <div id="docx-log" class="log" style="display: none;"></div>
        </div>
        
        <div class="test-item">
            <h3>4. html-docx-js库测试</h3>
            <p>测试轻量级的html-docx-js库</p>
            <button onclick="testHtmlDocxLibrary()">测试html-docx库</button>
            <span id="htmldocx-status" class="status loading">等待测试</span>
            <div id="htmldocx-log" class="log" style="display: none;"></div>
        </div>
        
        <div class="test-item">
            <h3>5. 生成测试文档</h3>
            <p>使用可用的库生成测试Word文档</p>
            <button onclick="generateTestDocument()">生成测试文档</button>
            <span id="generate-status" class="status loading">等待生成</span>
            <div id="generate-log" class="log" style="display: none;"></div>
        </div>
    </div>

    <!-- 动态加载库进行测试 -->
    <script>
        function log(elementId, message) {
            const logElement = document.getElementById(elementId);
            logElement.style.display = 'block';
            logElement.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
            logElement.scrollTop = logElement.scrollHeight;
        }

        function setStatus(elementId, status, text) {
            const statusElement = document.getElementById(elementId);
            statusElement.className = `status ${status}`;
            statusElement.textContent = text;
        }

        function testBasicEnvironment() {
            const logId = 'basic-log';
            document.getElementById(logId).innerHTML = '';
            
            log(logId, '开始基础环境检测...');
            
            // 检测基础功能
            const tests = [
                { name: 'Promise支持', test: () => typeof Promise !== 'undefined' },
                { name: 'Fetch API支持', test: () => typeof fetch !== 'undefined' },
                { name: 'Blob支持', test: () => typeof Blob !== 'undefined' },
                { name: 'URL.createObjectURL支持', test: () => typeof URL !== 'undefined' && typeof URL.createObjectURL !== 'undefined' },
                { name: 'Document.createElement支持', test: () => typeof document.createElement !== 'undefined' }
            ];
            
            let allPassed = true;
            tests.forEach(test => {
                const result = test.test();
                log(logId, `${test.name}: ${result ? '✓ 支持' : '✗ 不支持'}`);
                if (!result) allPassed = false;
            });
            
            if (allPassed) {
                setStatus('basic-status', 'success', '环境检测通过');
                log(logId, '✓ 基础环境检测完成，所有功能都支持');
            } else {
                setStatus('basic-status', 'error', '环境检测失败');
                log(logId, '✗ 部分基础功能不支持，可能影响库的正常工作');
            }
        }

        async function testCDNConnections() {
            const logId = 'cdn-log';
            document.getElementById(logId).innerHTML = '';
            
            log(logId, '开始CDN连接测试...');
            
            const cdnUrls = [
                'https://unpkg.com/docx@9.5.1/build/index.js',
                'https://cdn.jsdelivr.net/npm/docx@9.5.1/build/index.js',
                'https://unpkg.com/html-docx-js@0.3.1/dist/html-docx.js',
                'https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js'
            ];
            
            let successCount = 0;
            
            for (const url of cdnUrls) {
                try {
                    const response = await fetch(url, { method: 'HEAD' });
                    if (response.ok) {
                        log(logId, `✓ ${url} - 连接成功`);
                        successCount++;
                    } else {
                        log(logId, `✗ ${url} - 连接失败 (${response.status})`);
                    }
                } catch (error) {
                    log(logId, `✗ ${url} - 连接错误: ${error.message}`);
                }
            }
            
            if (successCount === cdnUrls.length) {
                setStatus('cdn-status', 'success', '所有CDN连接正常');
            } else if (successCount > 0) {
                setStatus('cdn-status', 'success', `部分CDN可用 (${successCount}/${cdnUrls.length})`);
            } else {
                setStatus('cdn-status', 'error', 'CDN连接失败');
            }
        }

        function testDocxLibrary() {
            const logId = 'docx-log';
            document.getElementById(logId).innerHTML = '';
            
            log(logId, '开始加载docx库...');
            
            // 动态加载docx库
            const script = document.createElement('script');
            script.src = 'https://unpkg.com/docx@9.5.1/build/index.js';
            
            script.onload = () => {
                log(logId, '✓ docx库加载成功');
                
                // 测试基本功能
                try {
                    if (typeof docx !== 'undefined') {
                        log(logId, '✓ docx对象可用');
                        log(logId, `docx版本信息: ${JSON.stringify(Object.keys(docx))}`);
                        
                        // 测试创建简单文档
                        const doc = new docx.Document({
                            sections: [{
                                properties: {},
                                children: [
                                    new docx.Paragraph({
                                        children: [new docx.TextRun("测试文档")]
                                    })
                                ]
                            }]
                        });
                        
                        log(logId, '✓ 文档创建测试成功');
                        setStatus('docx-status', 'success', 'docx库测试通过');
                    } else {
                        throw new Error('docx对象未定义');
                    }
                } catch (error) {
                    log(logId, `✗ docx库功能测试失败: ${error.message}`);
                    setStatus('docx-status', 'error', 'docx库功能异常');
                }
            };
            
            script.onerror = () => {
                log(logId, '✗ docx库加载失败');
                setStatus('docx-status', 'error', 'docx库加载失败');
            };
            
            document.head.appendChild(script);
        }

        function testHtmlDocxLibrary() {
            const logId = 'htmldocx-log';
            document.getElementById(logId).innerHTML = '';
            
            log(logId, '开始加载html-docx-js库...');
            
            // 动态加载html-docx-js库
            const script = document.createElement('script');
            script.src = 'https://unpkg.com/html-docx-js@0.3.1/dist/html-docx.js';
            
            script.onload = () => {
                log(logId, '✓ html-docx-js库加载成功');
                
                // 测试基本功能
                try {
                    if (typeof htmlDocx !== 'undefined') {
                        log(logId, '✓ htmlDocx对象可用');
                        
                        // 测试转换功能
                        const testHtml = '<html><body><h1>测试</h1></body></html>';
                        const blob = htmlDocx.asBlob(testHtml);
                        
                        if (blob instanceof Blob) {
                            log(logId, '✓ HTML转Word功能测试成功');
                            setStatus('htmldocx-status', 'success', 'html-docx库测试通过');
                        } else {
                            throw new Error('转换结果不是Blob对象');
                        }
                    } else {
                        throw new Error('htmlDocx对象未定义');
                    }
                } catch (error) {
                    log(logId, `✗ html-docx库功能测试失败: ${error.message}`);
                    setStatus('htmldocx-status', 'error', 'html-docx库功能异常');
                }
            };
            
            script.onerror = () => {
                log(logId, '✗ html-docx-js库加载失败');
                setStatus('htmldocx-status', 'error', 'html-docx库加载失败');
            };
            
            document.head.appendChild(script);
        }

        function generateTestDocument() {
            const logId = 'generate-log';
            document.getElementById(logId).innerHTML = '';
            
            log(logId, '开始生成测试文档...');
            
            // 首先加载FileSaver.js
            const fileSaverScript = document.createElement('script');
            fileSaverScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js';
            
            fileSaverScript.onload = () => {
                log(logId, '✓ FileSaver.js加载成功');
                
                // 尝试使用html-docx-js生成文档
                if (typeof htmlDocx !== 'undefined' && typeof saveAs !== 'undefined') {
                    try {
                        const testHtml = `
                            <html>
                            <head><meta charset="utf-8"></head>
                            <body>
                                <h1 style="text-align: center;">测试文档</h1>
                                <p>这是一个测试生成的Word文档。</p>
                                <p>生成时间: ${new Date().toLocaleString()}</p>
                            </body>
                            </html>
                        `;
                        
                        const blob = htmlDocx.asBlob(testHtml);
                        saveAs(blob, '测试文档.docx');
                        
                        log(logId, '✓ 测试文档生成成功并开始下载');
                        setStatus('generate-status', 'success', '文档生成成功');
                    } catch (error) {
                        log(logId, `✗ 文档生成失败: ${error.message}`);
                        setStatus('generate-status', 'error', '文档生成失败');
                    }
                } else {
                    log(logId, '✗ 必要的库未加载完成');
                    setStatus('generate-status', 'error', '库未就绪');
                }
            };
            
            fileSaverScript.onerror = () => {
                log(logId, '✗ FileSaver.js加载失败');
                setStatus('generate-status', 'error', 'FileSaver加载失败');
            };
            
            document.head.appendChild(fileSaverScript);
        }
    </script>
</body>
</html>
