<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试Word生成</title>
</head>
<body>
    <h1>测试Word文档生成</h1>
    <button onclick="testDocx()">测试生成Word</button>
    
    <script src="https://unpkg.com/docx@8.5.0/build/index.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
    
    <script>
        async function testDocx() {
            try {
                console.log('docx对象:', docx);
                
                const doc = new docx.Document({
                    sections: [{
                        properties: {},
                        children: [
                            new docx.Paragraph({
                                children: [
                                    new docx.TextRun({
                                        text: "测试文档",
                                        bold: true,
                                        size: 32,
                                    }),
                                ],
                                alignment: docx.AlignmentType.CENTER,
                            }),
                        ],
                    }],
                });
                
                const blob = await docx.Packer.toBlob(doc);
                saveAs(blob, "test.docx");
                alert('测试成功！');
                
            } catch (error) {
                console.error('错误:', error);
                alert('错误: ' + error.message);
            }
        }
    </script>
</body>
</html>
