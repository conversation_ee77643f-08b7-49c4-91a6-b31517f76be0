# Word模板功能使用说明

## 功能概述

新版加班审批表生成器支持自定义Word模板功能，用户可以上传自己的Word模板文件，系统会自动将表单数据填入模板中的占位符位置。

## 模板制作指南

### 1. 创建Word模板

1. 使用Microsoft Word创建一个新文档
2. 设计你想要的表格布局、字体样式、页面格式等
3. 在需要填入数据的位置插入占位符

### 2. 占位符规范

在Word模板中使用以下占位符格式：

```
{{部门}}    - 工作部门
{{姓名}}    - 加班人姓名  
{{事由}}    - 加班事由
{{地点}}    - 加班地点
{{日期}}    - 加班日期列表
{{当前日期}} - 申请日期
```

### 3. 占位符使用示例

```
加班审批表

工作部门：{{部门}}                    加班人：{{姓名}}
加班事由：{{事由}}
加班地点：{{地点}}
加班时间：{{日期}}

申请日期：{{当前日期}}

部门负责人意见：                     分管领导意见：



签字：                              签字：
日期：                              日期：
```

## 使用步骤

### 1. 上传模板

1. 点击"点击或拖拽上传Word模板"区域
2. 选择你制作的 .docx 格式模板文件
3. 系统会自动验证模板并显示发现的占位符

### 2. 填写表单

1. 在表单中填写工作部门、加班人、加班事由、加班地点
2. 选择加班日期
3. 确保所有必填信息都已填写完整

### 3. 生成文档

1. 点击"生成并下载Word文档"按钮
2. 系统会使用你的模板生成文档
3. 所有占位符会被替换为对应的表单数据
4. 生成的文档会自动下载

## 模板功能

### 模板验证
- 系统会自动检测模板中的占位符
- 显示发现的占位符数量和列表
- 提供模板格式验证

### 模板预览
- 点击"预览模板"查看模板信息
- 确认占位符位置是否正确

### 默认模板
- 如果不上传自定义模板，系统使用内置默认模板
- 点击"使用默认模板"可切换回默认模式

### 模板管理
- 支持重新上传模板文件
- 可以清除当前模板
- 支持拖拽上传

## 注意事项

### 文件要求
- 只支持 .docx 格式的Word文档
- 文件大小不能超过10MB
- 建议使用Microsoft Word 2007或更高版本创建

### 占位符要求
- 占位符必须使用双花括号 {{}} 格式
- 占位符名称区分大小写
- 确保占位符拼写正确

### 格式保持
- 模板的所有格式设置都会保留
- 包括字体、颜色、表格样式、页面布局等
- 只有占位符内容会被替换

## 常见问题

### Q: 为什么上传模板后没有发现占位符？
A: 请检查占位符格式是否正确，必须使用 {{占位符名称}} 的格式，注意大小写。

### Q: 生成的文档格式不正确怎么办？
A: 请确保模板文件是有效的 .docx 格式，并且没有损坏。

### Q: 可以在模板中使用表格吗？
A: 可以，模板支持所有Word格式，包括表格、图片、样式等。

### Q: 占位符可以重复使用吗？
A: 可以，同一个占位符可以在模板中多次使用，都会被替换为相同的内容。

## 技术支持

如果在使用过程中遇到问题，请：
1. 检查模板文件格式是否正确
2. 确认占位符拼写是否准确
3. 验证网络连接是否正常
4. 尝试使用默认模板功能

## 更新日志

- v2.0：新增自定义模板功能
- 支持Word模板上传和占位符替换
- 添加模板验证和预览功能
- 保持原有默认模板功能
