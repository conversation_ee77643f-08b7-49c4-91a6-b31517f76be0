<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加班审批表生成器（简化版）</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            color: #34495e;
        }
        input, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            resize: vertical;
            min-height: 80px;
        }
        .date-input {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .date-input input {
            flex: 1;
            min-width: 150px;
        }
        .export-btn {
            width: 100%;
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
            border: none;
            padding: 15px;
            font-size: 16px;
            font-weight: bold;
            border-radius: 8px;
            cursor: pointer;
            margin-top: 20px;
        }
        .export-btn:hover {
            background: linear-gradient(135deg, #229954, #1e8449);
        }
        .export-btn:disabled {
            background: #95a5a6;
            cursor: not-allowed;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            display: none;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>加班审批表生成器</h1>
        
        <form id="overtimeForm">
            <div class="form-group">
                <label for="department">工作部门</label>
                <input type="text" id="department" required>
            </div>
            
            <div class="form-group">
                <label for="employee">加班人</label>
                <input type="text" id="employee" required>
            </div>
            
            <div class="form-group">
                <label for="reason">加班事由</label>
                <textarea id="reason" required></textarea>
            </div>
            
            <div class="form-group">
                <label for="location">加班地点</label>
                <input type="text" id="location" required>
            </div>
            
            <div class="form-group">
                <label>加班日期（可添加多个）</label>
                <div class="date-input">
                    <input type="date" id="date1" required>
                    <input type="date" id="date2">
                    <input type="date" id="date3">
                </div>
            </div>
            
            <button type="button" class="export-btn" onclick="generateDocument()">
                生成并下载Word文档
            </button>
            
            <div id="status" class="status"></div>
        </form>
    </div>

    <!-- 使用html-docx-js库，更轻量且稳定 -->
    <script src="https://unpkg.com/html-docx-js@0.3.1/dist/html-docx.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
    
    <script>
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            
            if (type === 'success') {
                setTimeout(() => {
                    status.style.display = 'none';
                }, 3000);
            }
        }

        function generateDocument() {
            try {
                // 获取表单数据
                const department = document.getElementById('department').value.trim();
                const employee = document.getElementById('employee').value.trim();
                const reason = document.getElementById('reason').value.trim();
                const location = document.getElementById('location').value.trim();
                
                // 验证必填字段
                if (!department || !employee || !reason || !location) {
                    showStatus('请填写所有必填信息', 'error');
                    return;
                }
                
                // 获取日期
                const dates = [];
                for (let i = 1; i <= 3; i++) {
                    const dateValue = document.getElementById(`date${i}`).value;
                    if (dateValue) {
                        const date = new Date(dateValue);
                        const dayOfWeek = ['日', '一', '二', '三', '四', '五', '六'][date.getDay()];
                        dates.push(`${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日(星期${dayOfWeek})`);
                    }
                }
                
                if (dates.length === 0) {
                    showStatus('请至少选择一个加班日期', 'error');
                    return;
                }
                
                // 生成HTML内容
                const htmlContent = `
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset="utf-8">
                        <style>
                            body { font-family: '宋体', SimSun, serif; font-size: 14px; line-height: 1.6; }
                            .title { text-align: center; font-size: 18px; font-weight: bold; margin-bottom: 20px; }
                            .info-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                            .info-table td { border: 1px solid #000; padding: 8px; }
                            .label { background-color: #f0f0f0; width: 20%; font-weight: bold; }
                            .approval-table { width: 100%; border-collapse: collapse; margin-top: 30px; }
                            .approval-table td { border: 1px solid #000; padding: 15px; vertical-align: top; }
                            .note { margin-top: 20px; font-size: 12px; }
                        </style>
                    </head>
                    <body>
                        <div class="title">加班审批表</div>
                        
                        <table class="info-table">
                            <tr>
                                <td class="label">工作部门</td>
                                <td>${department}</td>
                                <td class="label">加班人</td>
                                <td>${employee}</td>
                            </tr>
                            <tr>
                                <td class="label">加班事由</td>
                                <td colspan="3">${reason}</td>
                            </tr>
                            <tr>
                                <td class="label">加班地点</td>
                                <td colspan="3">${location}</td>
                            </tr>
                            <tr>
                                <td class="label">加班时间</td>
                                <td colspan="3">${dates.join('、')}</td>
                            </tr>
                        </table>
                        
                        <table class="approval-table">
                            <tr>
                                <td style="width: 50%;">
                                    <strong>部门负责人意见：</strong><br><br><br><br>
                                    签字：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日期：
                                </td>
                                <td style="width: 50%;">
                                    <strong>分管领导意见：</strong><br><br><br><br>
                                    签字：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日期：
                                </td>
                            </tr>
                        </table>
                        
                        <div class="note">
                            <strong>备注：</strong><br>
                            1. 加班需提前申请，经部门负责人或分管领导批准后方可执行；<br>
                            2. 本表一式两份，一份报人事部门备案，一份部门留存。
                        </div>
                    </body>
                    </html>
                `;
                
                // 转换为Word文档
                const converted = htmlDocx.asBlob(htmlContent);
                const fileName = `${department}-${employee}-加班审批表-${new Date().getFullYear()}年${new Date().getMonth() + 1}月.docx`;
                
                // 下载文件
                saveAs(converted, fileName);
                showStatus('Word文档已生成并开始下载！', 'success');
                
            } catch (error) {
                console.error('生成文档时出错：', error);
                showStatus(`生成文档时出错：${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
